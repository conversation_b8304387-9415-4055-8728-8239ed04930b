package common

import (
	"strings"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/rs/zerolog/log"
)

func GenPDF(htmlStr string, firstVisit *bool) (pdfBytes []byte) {
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		log.Error().Err(err).Msg("Failed to create PDF generator")
		return nil
	}

	if firstVisit != nil && *firstVisit {
		// Add watermark options
		pdfg.Dpi.Set(PdfDPI)
		// Replace id="duplicate" with style="display:inline-block" in the HTML string
		htmlStr = strings.ReplaceAll(htmlStr, `id="duplicate"`, `style="display:inline-block"`)
	}

	page := wkhtmltopdf.NewPageReader(strings.NewReader(htmlStr))

	page.NoBackground.Set(true)
	page.DisableExternalLinks.Set(false)

	page.EnableLocalFileAccess.Set(true)

	// Add the HTML content to the PDF generator
	pdfg.AddPage(page)

	if firstVisit != nil && !*firstVisit {
		// Add watermark options
		pdfg.Dpi.Set(PdfDPI)
	}

	pdfg.Dpi.Set(PdfDPI)

	// Generate the PDF
	err = pdfg.Create()
	if err != nil {
		log.Error().Err(err).Msg("Failed to create PDF")
		return nil
	}

	return pdfg.Bytes()
}
